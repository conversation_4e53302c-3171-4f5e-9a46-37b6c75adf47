package com.lirong.module.aigc.core.service;

import com.lirong.module.aigc.core.service.impl.QueryOptimizerImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 查询优化器测试类
 * 
 * <AUTHOR> Assistant
 */
@ExtendWith(MockitoExtension.class)
class QueryOptimizerTest {
    
    @InjectMocks
    private QueryOptimizerImpl queryOptimizer;
    
    @Test
    void testDetectLanguage_Chinese() {
        String chineseText = "你好，世界";
        String result = queryOptimizer.detectLanguage(chineseText);
        assertEquals("zh", result);
    }
    
    @Test
    void testDetectLanguage_English() {
        String englishText = "Hello, world";
        String result = queryOptimizer.detectLanguage(englishText);
        assertEquals("en", result);
    }
    
    @Test
    void testDetectLanguage_Mixed() {
        String mixedText = "Hello 世界";
        String result = queryOptimizer.detectLanguage(mixedText);
        // 应该返回主要语言类型
        assertNotNull(result);
        assertTrue(result.equals("zh") || result.equals("en"));
    }
    
    @Test
    void testDetectLanguage_Empty() {
        String emptyText = "";
        String result = queryOptimizer.detectLanguage(emptyText);
        assertEquals("other", result);
    }
    
    @Test
    void testDetectLanguage_Null() {
        String result = queryOptimizer.detectLanguage(null);
        assertEquals("other", result);
    }
    
    @Test
    void testOptimizeQuery_NotNull() {
        String originalQuery = "机器学习";
        String result = queryOptimizer.optimizeQuery(originalQuery);
        assertNotNull(result);
        assertFalse(result.trim().isEmpty());
    }
    
    @Test
    void testOptimizeQuery_Empty() {
        String emptyQuery = "";
        String result = queryOptimizer.optimizeQuery(emptyQuery);
        assertEquals(emptyQuery, result);
    }
    
    @Test
    void testOptimizeQuery_Null() {
        String result = queryOptimizer.optimizeQuery(null);
        assertNull(result);
    }
}
