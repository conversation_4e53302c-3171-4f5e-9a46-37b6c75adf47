package com.lirong.module.aigc.core.rag;

import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.rag.content.Content;
import dev.langchain4j.rag.content.retriever.ContentRetriever;
import dev.langchain4j.rag.query.Query;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingSearchResult;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.filter.Filter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 优化的内容检索器
 * 使用优化后的查询文本进行语义检索
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
public class OptimizedContentRetriever implements ContentRetriever {
    
    private final EmbeddingStore<TextSegment> embeddingStore;
    private final EmbeddingModel embeddingModel;
    private final String optimizedQuery;
    private final Filter filter;
    
    public OptimizedContentRetriever(EmbeddingStore<TextSegment> embeddingStore,
                                   EmbeddingModel embeddingModel,
                                   String optimizedQuery,
                                   Filter filter) {
        this.embeddingStore = embeddingStore;
        this.embeddingModel = embeddingModel;
        this.optimizedQuery = optimizedQuery;
        this.filter = filter;
    }
    
    @Override
    public List<Content> retrieve(Query query) {
        try {
            log.info("使用优化查询进行检索: {}", optimizedQuery);
            
            // 使用优化后的查询文本进行嵌入
            Embedding queryEmbedding = embeddingModel.embed(optimizedQuery).content();
            
            // 构建检索请求
            EmbeddingSearchRequest searchRequest = EmbeddingSearchRequest.builder()
                    .queryEmbedding(queryEmbedding)
                    .maxResults(80)
                    .minScore(0.6)
                    .filter(filter)
                    .build();
            
            // 执行检索
            EmbeddingSearchResult<TextSegment> searchResult = embeddingStore.search(searchRequest);
            
            // 转换为Content列表
            List<Content> contents = searchResult.matches().stream()
                    .map(match -> Content.from(match.embedded()))
                    .collect(Collectors.toList());
            
            log.info("检索完成，找到 {} 个相关内容", contents.size());
            return contents;
            
        } catch (Exception e) {
            log.error("检索过程中发生错误", e);
            // 如果优化查询失败，回退到原始查询
            return retrieveWithOriginalQuery(query);
        }
    }
    
    /**
     * 使用原始查询进行检索（回退方案）
     */
    private List<Content> retrieveWithOriginalQuery(Query query) {
        try {
            log.info("回退到原始查询进行检索: {}", query.text());
            
            Embedding queryEmbedding = embeddingModel.embed(query.text()).content();
            
            EmbeddingSearchRequest searchRequest = EmbeddingSearchRequest.builder()
                    .queryEmbedding(queryEmbedding)
                    .maxResults(80)
                    .minScore(0.6)
                    .filter(filter)
                    .build();
            
            EmbeddingSearchResult<TextSegment> searchResult = embeddingStore.search(searchRequest);
            
            return searchResult.matches().stream()
                    .map(match -> Content.from(match.embedded()))
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("原始查询检索也失败", e);
            return List.of();
        }
    }
}
