package com.lirong.module.aigc.core.service;

/**
 * 查询优化服务接口
 * 负责对用户查询进行优化，包括语言检测、翻译、查询扩展等
 * 
 * <AUTHOR> Assistant
 */
public interface QueryOptimizer {
    
    /**
     * 优化查询文本
     * 检测语言并进行必要的翻译和优化，提高语义检索的准确性
     * 
     * @param originalQuery 原始查询文本
     * @return 优化后的查询文本
     */
    String optimizeQuery(String originalQuery);
    
    /**
     * 检测文本语言
     * 
     * @param text 待检测的文本
     * @return 语言类型：zh（中文）、en（英文）、other（其他）
     */
    String detectLanguage(String text);
    
    /**
     * 翻译文本
     * 
     * @param text 待翻译的文本
     * @param sourceLanguage 源语言
     * @param targetLanguage 目标语言
     * @return 翻译后的文本
     */
    String translateText(String text, String sourceLanguage, String targetLanguage);
}
