package com.lirong.module.aigc.core.functions;

import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;
import dev.langchain4j.service.V;

/**
 * 查询优化AI服务接口
 * 使用AI模型进行查询优化和语言检测
 * 
 * <AUTHOR> Assistant
 */
public interface QueryOptimizerAI {
    
    /**
     * 检测文本语言
     */
    @SystemMessage("你是一个语言检测专家。请检测给定文本的主要语言类型。只返回语言代码：zh（中文）、en（英文）、other（其他语言）。")
    @UserMessage("请检测以下文本的语言类型：{{text}}")
    String detectLanguage(@V("text") String text);
    
    /**
     * 优化查询文本
     */
    @SystemMessage({
        "你是一个查询优化专家。你的任务是优化用户的查询文本，使其更适合进行语义检索。",
        "优化规则：",
        "1. 如果是中文查询，保持中文，但可以添加相关的英文关键词来增强语义",
        "2. 如果是英文查询，保持英文，但可以添加相关的中文关键词来增强语义", 
        "3. 扩展同义词和相关概念",
        "4. 保持查询的核心意图不变",
        "5. 返回优化后的查询文本，不要添加额外的解释"
    })
    @UserMessage("请优化以下查询文本以提高语义检索效果：{{query}}")
    String optimizeQuery(@V("query") String query);
    
    /**
     * 翻译文本
     */
    @SystemMessage("你是一个专业的翻译专家。请将给定的文本从源语言翻译为目标语言。保持原意，使用自然流畅的表达。只返回翻译结果，不要添加额外说明。")
    @UserMessage("请将以下文本从{{sourceLanguage}}翻译为{{targetLanguage}}：{{text}}")
    String translateText(@V("text") String text, @V("sourceLanguage") String sourceLanguage, @V("targetLanguage") String targetLanguage);
}
