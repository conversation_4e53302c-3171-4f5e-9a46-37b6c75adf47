import com.pdftron.pdf.PDFNet;

public class PDFNetTest {
    public static void main(String[] args) {
        boolean pdfNetAvailable = false;
        
        try {
            PDFNet.initialize("demo:<EMAIL>:61759e140200000000f86322f6a371657057742c0d563de52f999d61ad");
            pdfNetAvailable = true;
            System.out.println("✅ PDFNet initialized successfully");
        } catch (UnsatisfiedLinkError e) {
            System.out.println("⚠️  PDFNet native library not found, PDF processing will be limited: " + e.getMessage());
            pdfNetAvailable = false;
        } catch (Exception e) {
            System.out.println("❌ Failed to initialize PDFNet: " + e.getMessage());
            pdfNetAvailable = false;
        }
        
        System.out.println("PDFNet Available: " + pdfNetAvailable);
        
        if (pdfNetAvailable) {
            try {
                PDFNet.terminate();
                System.out.println("✅ PDFNet terminated successfully");
            } catch (Exception e) {
                System.out.println("⚠️  Failed to terminate PDFNet: " + e.getMessage());
            }
        } else {
            System.out.println("ℹ️  Application will use PDFBox fallback for PDF processing");
        }
    }
}
