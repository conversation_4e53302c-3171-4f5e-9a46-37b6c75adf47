package com.lirong.module.doc.service.documents;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.aspose.pdf.Document;
import com.aspose.pdf.HtmlLoadOptions;
import com.aspose.pdf.PageInfo;
import com.aspose.pdf.SaveFormat;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lirong.framework.common.exception.ServiceException;
import com.lirong.framework.common.util.file.FileDownloader;
import com.lirong.framework.common.util.file.FileHashUtil;
import com.lirong.framework.common.util.retry.RetryUtil;
import com.lirong.framework.common.util.validation.ValidationUtils;
import com.lirong.module.doc.controller.admin.crawler.ProxyController;
import com.lirong.module.doc.controller.admin.documentexperts.vo.DocumentExpertsSaveReqVO;
import com.lirong.module.doc.dal.dataobject.categories.CategoriesDO;
import com.lirong.module.doc.dal.dataobject.documentexperts.DocumentExpertsDO;
import com.lirong.module.doc.dal.dataobject.institutions.InstitutionsDO;
import com.lirong.module.doc.service.categories.CategoriesService;
import com.lirong.module.doc.service.documentexperts.DocumentExpertsService;
import com.lirong.module.doc.service.experts.ExpertsService;
import com.lirong.module.doc.service.institutions.InstitutionsService;
import com.lirong.module.doc.service.model.DocumentInfo;
import com.lirong.module.doc.service.model.DocumentInfoExtractor;
import com.lirong.module.doc.task.FindUnSyncTask;
import com.lirong.module.doc.task.SyncDoc2EsTask;
import com.lirong.module.infra.api.file.FileApi;
import com.lowagie.text.PageSize;
import com.pdftron.common.PDFNetException;
import com.pdftron.pdf.*;
import io.minio.errors.ErrorResponseException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.concurrent.Callable;

import com.lirong.module.doc.controller.admin.documents.vo.*;
import com.lirong.module.doc.dal.dataobject.documents.DocumentsDO;
import com.lirong.framework.common.pojo.PageResult;
import com.lirong.framework.common.util.object.BeanUtils;

import com.lirong.module.doc.dal.mysql.documents.DocumentsMapper;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.imageio.ImageIO;

import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.Year;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.lirong.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.lirong.module.doc.enums.ErrorCodeConstants.*;

/**
 * 文献 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class DocumentsServiceImpl implements DocumentsService {

    @Resource
    private DocumentsMapper documentsMapper;

    @Resource
    private ExpertsService expertsService;

    @Resource
    private DocumentExpertsService documentExpertsService;

    @Resource
    private InstitutionsService institutionsService;

    @Resource
    private CategoriesService categoriesService;
    @Resource
    private FindUnSyncTask findUnSyncTask;

    @Resource
    private FileApi fileApi;

    @Resource(name = "documentImportExecutor")
    private ExecutorService executorService;

    @Resource
    private ProxyController proxyController;

    private boolean pdfNetAvailable = false;

    @PostConstruct
    public void initPDFNet() {
        try {
            PDFNet.initialize("demo:<EMAIL>:61759e140200000000f86322f6a371657057742c0d563de52f999d61ad");
            pdfNetAvailable = true;
            log.info("PDFNet initialized successfully");
        } catch (UnsatisfiedLinkError e) {
            log.warn("PDFNet native library not found, PDF processing will be limited: {}", e.getMessage());
            pdfNetAvailable = false;
        } catch (Exception e) {
            log.error("Failed to initialize PDFNet: {}", e.getMessage(), e);
            pdfNetAvailable = false;
        }
    }

    @Override
    public Long createDocuments(DocumentsSaveReqVO createReqVO) {
        // 插入
        DocumentsDO documents = BeanUtils.toBean(createReqVO, DocumentsDO.class);
        documentsMapper.insert(documents);
        // 添加到同步ES队列
        try {
            findUnSyncTask.manualSync();
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 返回
        return documents.getId();
    }

    @Override
    public Long createDocumentFromFile(DocumentsSaveReqVO createReqVO) {
        DocumentsDO documents = BeanUtils.toBean(createReqVO, DocumentsDO.class);
        documentsMapper.insert(documents);
        Long docId = documents.getId();

        createReqVO.getAuthorIds().forEach(author -> {
            try {
                DocumentExpertsSaveReqVO reqVO = new DocumentExpertsSaveReqVO();
                reqVO.setDocumentId(docId);
                reqVO.setExpertId(author);
                documentExpertsService.createDocumentExperts(reqVO);
            } catch (Exception e) {
//                e.printStackTrace();
                System.out.println("文献作者已存在");
            }
        });
        return docId;
    }

    @Override
    public void updateDocuments(DocumentsSaveReqVO updateReqVO) {
        // 校验存在
        validateDocumentsExists(updateReqVO.getId());
        // 更新
        DocumentsDO updateObj = BeanUtils.toBean(updateReqVO, DocumentsDO.class);
        documentsMapper.updateById(updateObj);
    }

    @Override
    public void deleteDocuments(Long id) {
        // 校验存在
        validateDocumentsExists(id);
        // 删除
        documentsMapper.deleteById(id);
    }

    private void validateDocumentsExists(Long id) {
        if (documentsMapper.selectById(id) == null) {
            throw exception(DOCUMENTS_NOT_EXISTS);
        }
    }

    @Override
    public DocumentsDO getDocuments(Long id) {
        List<DocumentExpertsDO> documentExpertsDOS = documentExpertsService.selectByDocument(id);
        DocumentsDO documentsDO = documentsMapper.selectById(id);
        documentsDO.setAuthorIds(documentExpertsDOS.stream().map(DocumentExpertsDO::getExpertId).collect(Collectors.toList()));


        return documentsDO;
    }

    @Override
    public PageResult<DocumentsDO> getDocumentsPage(DocumentsPageReqVO pageReqVO) {
        // 创建分页对象
        IPage<DocumentsDO> page = new Page(pageReqVO.getPageNo(), pageReqVO.getPageSize());

        // 执行分页查询
        IPage<DocumentsDO> result = documentsMapper.selectPage(page, pageReqVO);

        // 转换为自定义的 PageResult
        return new PageResult<>(result.getRecords(), result.getTotal());
    }

    @Override
    public DocumentsRespVO uploadAndParse(MultipartFile file) throws IOException {
        String id = FileHashUtil.generateFileHash(file);
        String suffix = FileUtil.getSuffix(file.getOriginalFilename());
        String path = "document/" + id + "." + suffix;

        // 读取文件内容（只读取一次，避免多次读取导致流关闭）
        byte[] fileContent = IoUtil.readBytes(file.getInputStream());

        // 上传文件（使用重试机制）
        String fileUrl;
        try {
            fileUrl = uploadFileWithRetry(file.getOriginalFilename(), path, fileContent);
        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            if (e instanceof IOException) {
                throw (IOException) e;
            }
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }

        // 处理PDF
        DocumentInfo documentInfo = processPDF(new ByteArrayInputStream(fileContent), id);

        // 构建返回对象
        DocumentsRespVO respVO = new DocumentsRespVO();
        respVO.setChineseTitle(documentInfo.getTitleCn());
        respVO.setForeignTitle(documentInfo.getTitle());
        respVO.setLanguage(documentInfo.getLanguage());
        respVO.setChineseAbstract(documentInfo.getSummaryCn());
        respVO.setForeignAbstract(documentInfo.getSummary());
        respVO.setPageCount(documentInfo.getPageCount());
        respVO.setWordCount(documentInfo.getWordCount());
        respVO.setOriginalUrl(fileUrl);
        respVO.setCategoryName(documentInfo.getCategory());
        if (StringUtils.isNotBlank(documentInfo.getTags())) {
            respVO.setTags(documentInfo.getTags().replaceAll("；", ",").replaceAll(";", ",").replaceAll("，", ",").replaceAll("、", ",").split(","));
        }
        respVO.setThumbnailUrl(documentInfo.getSmallThumbnailPath());
        if (documentInfo.getTags() != null) {
            respVO.setAuthors(List.of(documentInfo.getAuthor().replaceAll("；", ",").replaceAll(";", ",").replaceAll("，", ",").replaceAll("、", ",").split(",")));
        }
        respVO.setPublisher(documentInfo.getPublisher());
        String date = documentInfo.getDate();
        if (StringUtils.isNotBlank(date)) {
            try {
                String[] dates = convertDate(date).split("-");

                // 确保至少有年份
                if (dates.length >= 1) {
                    respVO.setPublishYear(Integer.parseInt(dates[0]));
                }
                // 如果有月份，则设置月份
                if (dates.length >= 2) {
                    respVO.setPublishMonth(Integer.parseInt(dates[1]));
                }
                // 如果有日期，则设置日期
                if (dates.length >= 3) {
                    respVO.setPublishDay(Integer.parseInt(dates[2]));
                }
            } catch (Exception e) {
                System.out.println("日期格式不正确，无法解析");
            }
        }

        return respVO;
    }

    // 定义可能的日期格式
    private final String[] DATE_FORMATS = {
            "MMMM yyyy",       // November 2023
            "MMMM, yyyy",       // November 2023
            "MMMM dd, yyyy",   // July 17, 2024
            "MMMM d, yyyy",   // November 7, 2014
            "d MMMM yyyy",      // 10 February 2025
            "dd MMMM yyyy",     // 10 February 2025（两位日期）
            "yyyy-MM-dd",      // 2023-11-01
            "yyyy-MM",         // 2023-11
            "yyyy",             // 2023
            "MMM dd, yyyy",     // Jan 21, 2025
            "MMM d, yyyy"       // Nov 6, 2024

    };

    // 自动判断输入格式并转换为合适的输出格式
    public String convertDate(String inputDate) {
        // 替换多个空格为单个并去除首尾空格
        inputDate = inputDate.replaceAll("\\s+", " ").trim();
        for (String format : DATE_FORMATS) {
            try {
                DateTimeFormatter inputFormatter = new DateTimeFormatterBuilder()
                        .parseCaseInsensitive() // 忽略大小写
                        .appendPattern(format)
                        .toFormatter(Locale.ENGLISH);
                if (format.equals("MMMM yyyy") || format.equals("MMMM, yyyy") || format.equals("yyyy-MM")) {
                    YearMonth yearMonth = YearMonth.parse(inputDate, inputFormatter);
                    return yearMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                } else if (format.equals("yyyy")) {
                    Year year = Year.parse(inputDate, inputFormatter);
                    return year.format(DateTimeFormatter.ofPattern("yyyy"));
                } else {
                    LocalDate date = LocalDate.parse(inputDate, inputFormatter);
                    return date.format(DateTimeFormatter.ofPattern(getOutputFormat(format)));
                }
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式
            }
        }
        throw new IllegalArgumentException("无法解析日期: " + inputDate);
    }

    // 根据输入格式决定输出格式
    private String getOutputFormat(String inputFormat) {
        switch (inputFormat) {
            case "MMMM yyyy":
            case "MMMM, yyyy":
            case "yyyy-MM":
                return "yyyy-MM"; // November 2023 -> 2023-11
            case "MMMM dd, yyyy":
            case "MMMM d, yyyy":
            case "MMM dd, yyyy":
            case "MMM d, yyyy":
            case "d MMMM yyyy":   // 新增格式
            case "dd MMMM yyyy":   // 新增格式
                return "yyyy-MM-dd"; // July 17, 2024 -> 2024-07-17
            case "yyyy-MM-dd":
                return "yyyy-MM-dd"; // 2023-11-01 -> 2023-11-01
            case "yyyy":
                return "yyyy"; // 2023 -> 2023
            default:
                throw new IllegalArgumentException("不支持的日期格式: " + inputFormat);
        }
    }

    public static void main(String[] args) {
        DocumentsServiceImpl converter = new DocumentsServiceImpl();
        String result = converter.convertDate("November 7, 2014");
        System.out.println(result); // 应该输出 2015-01-05
    }

    private DocumentInfo processPDF(InputStream in, String id) {

        StringBuilder fullTextBuilder = new StringBuilder();

//        ByteArrayOutputStream baos = new ByteArrayOutputStream();
//        try {
//            in.transferTo(baos);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//        InputStream firstClone = new ByteArrayInputStream(baos.toByteArray());
//        InputStream secondClone = new ByteArrayInputStream(baos.toByteArray());


        DocumentInfo documentInfo = new DocumentInfo();
        Integer pageCount = 0;

        // 定义缩略图保存目录
        String thumbnailDir = "thumbnails";
        File dir = new File(thumbnailDir);
        if (!dir.exists()) {
            dir.mkdirs(); // 创建目录
        }
        // 生成缩略图路径
        String baseDir = System.getProperty("user.dir");
        String largeThumbnailPath = Paths.get(baseDir, thumbnailDir, id + "_thumbnail_large.png").toString();
        String smallThumbnailPath = Paths.get(baseDir, thumbnailDir, id + "_thumbnail_small.png").toString();
        String smallThumbnailUrl = "";
        String largeThumbnailUrl = "";

        // 加载PDF文档
//        PDDocument document = null;
//        try {
//            InputStream dest = IOUtils.toBufferedInputStream(firstClone);
//            document = PDDocument.load(dest);
//            // PDF渲染器
//            PDFRenderer pdfRenderer = new PDFRenderer(document);
//            // 渲染第一页为图像
//            BufferedImage bim = pdfRenderer.renderImageWithDPI(0, 300, ImageType.ARGB);
//            // 设置缩略图的大小，这里设置为宽度200像素，高度根据页面比例计算
//            int scale = 200;
//            // 创建缩略图
//            BufferedImage thumbnail = new BufferedImage(scale, (scale * bim.getHeight()) / bim.getWidth(), BufferedImage.TYPE_INT_ARGB);
//            thumbnail.getGraphics().drawImage(bim, 0, 0, thumbnail.getWidth(), thumbnail.getHeight(), null);
//
//            File smallOutputFile = new File(smallThumbnailPath);
//            ImageIO.write(thumbnail, "png", smallOutputFile);
//            // 保存缩略图为PNG文件
//            String smallPath = "document/" + smallOutputFile.getName();
//            smallThumbnailUrl = fileApi.createFile(smallOutputFile.getName(), smallPath, IoUtil.readBytes(new FileInputStream(smallOutputFile)));
//            FileUtil.del(smallOutputFile);
//
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        } finally {
//            if (document != null) {
//                // 关闭文档
//                try {
//                    document.close();
//                } catch (IOException e) {
//                    log.error("关闭PDF文档失败", e);
//                }
//            }
//        }
//        PDFNet.initialize("demo:<EMAIL>:61759e140200000000f86322f6a371657057742c0d563de52f999d61ad");
        // 使用 InputStream 加载 PDF 文件
        try (PDFDoc doc = new PDFDoc(in)) {
            PDFDraw draw = new PDFDraw();
            draw.setDPI(96); // 96 DPI 是常见的屏幕分辨率

            pageCount = doc.getPageCount();

            TextExtractor extractor = new TextExtractor();

            for (int i = 1; i < pageCount + 1; i++) {
                com.pdftron.pdf.Page page = doc.getPage(i);

                if (i == 1) {
                    // 生成大缩略图
                    BufferedImage largeImage = draw.getBitmap(page);
                    File largeOutputFile = new File(largeThumbnailPath);
                    ImageIO.write(largeImage, "png", largeOutputFile);
                    String largePath = "document/" + largeOutputFile.getName();

                    try {
                        // 使用重试机制上传大缩略图
                        byte[] largeImageContent = IoUtil.readBytes(new FileInputStream(largeOutputFile));
                        largeThumbnailUrl = uploadFileWithRetry(largeOutputFile.getName(), largePath, largeImageContent);
                    } catch (Exception e) {
                        log.error("上传大缩略图失败: {}", e.getMessage());
                    } finally {
                        FileUtil.del(largeOutputFile);
                    }

                    // 生成小缩略图
                    draw.setImageSize(160, 210);
                    BufferedImage smallImage = draw.getBitmap(page);
                    File smallOutputFile = new File(smallThumbnailPath);
                    ImageIO.write(smallImage, "png", smallOutputFile);

                    // 保存缩略图为PNG文件
                    String smallPath = "document/" + smallOutputFile.getName();

                    try {
                        // 使用重试机制上传小缩略图
                        byte[] smallImageContent = IoUtil.readBytes(new FileInputStream(smallOutputFile));
                        smallThumbnailUrl = uploadFileWithRetry(smallOutputFile.getName(), smallPath, smallImageContent);
                    } catch (Exception e) {
                        log.error("上传小缩略图失败: {}", e.getMessage());
                    } finally {
                        FileUtil.del(smallOutputFile);
                    }
                }

                // 提取文本
                extractor.begin(page);
                String text = extractor.getAsText();
                fullTextBuilder.append(text);
            }
        } catch (PDFNetException | IOException e) {
            e.printStackTrace();
        } finally {
            PDFNet.terminate();
        }

        // 处理提取的文本
        String fullText = fullTextBuilder.toString();
        int wordCount = fullText.split("\\s+").length; // 简单的字数统计

        // 创建抽取器
        DocumentInfoExtractor extractor = new DocumentInfoExtractor();
        // 抽取信息
        documentInfo = extractor.extractInfoFromText(fullText.substring(0, Math.min(fullText.length(), 7500)));
        documentInfo.setWordCount(wordCount);
        documentInfo.setPageCount(pageCount);
        documentInfo.setSmallThumbnailPath(smallThumbnailUrl);
        documentInfo.setLargeThumbnailPath(largeThumbnailUrl);

        return documentInfo;
    }

    @Override
    public DocumentImportRespVO importDocumentExcel(List<DocumentImportExcelVO> documentList) {
        DocumentImportRespVO respVO = DocumentImportRespVO.builder()
                .createRecords(new ArrayList<>())
                .updateRecords(new ArrayList<>())
                .ignoreRecords(new ArrayList<>())
                .failureRecords(new HashMap<>())
                .build();

        respVO.setTotalRecords(documentList.size());

        // 预校验：过滤有效文档（有原始URL或数据源URL）
        List<DocumentImportExcelVO> validDocuments = documentList.stream()
                .filter(doc -> StringUtils.isNotBlank(doc.getOriginalUrl()) || StringUtils.isNotBlank(doc.getDataSource()))
                .collect(Collectors.toList());

        List<DocumentImportExcelVO> noValidDocuments = documentList.stream()
                .filter(doc -> StringUtils.isBlank(doc.getOriginalUrl()) && StringUtils.isBlank(doc.getDataSource()))
                .collect(Collectors.toList());
        noValidDocuments.stream().forEach(doc -> respVO.getFailureRecords().put(doc.getForeignTitle(), "文件地址和数据源均为空"));

        // 批量查询已存在的文档
        Map<String, DocumentsDO> dataSourceDocMap = documentsMapper.selectList().stream()
                .collect(Collectors.toMap(
                        DocumentsDO::getDataSource,  // 键：dataSource 字段
                        doc -> doc,               // 值：整个文档对象
                        (existing, newDoc) -> {     // 冲突处理（可选）
                            // 如果有重复的 dataSource，保留最新的或根据业务需求处理
                            return existing; // 或者 newDoc
                        }
                ));

        // 批量处理准备
        List<DocumentExpertsDO> batchAuthorList = Collections.synchronizedList(new ArrayList<>());
        List<DocumentsDO> batchInsertList = Collections.synchronizedList(new ArrayList<>());
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (DocumentImportExcelVO importDoc : validDocuments) {
            String title = importDoc.getForeignTitle();
            Long institutionId = validateDocumentPublisher(importDoc.getPublisher());
            // 1. 验证阶段（集中处理异常）
            try {
                DocumentsSaveReqVO saveReq = BeanUtils.toBean(importDoc, DocumentsSaveReqVO.class);
                saveReq.setInstitutionId(institutionId);
                ValidationUtils.validate(saveReq);
            } catch (Exception e) {
                respVO.getFailureRecords().put(title, "字段验证失败: " + e.getMessage());
                continue;
            }

            // 2. 查询现有文档
            DocumentsDO existDoc = dataSourceDocMap.get(importDoc.getDataSource());

            // 3. 文件处理异步化（记录ID后异步执行）
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 根据existDoc判断插入或更新
                    if (existDoc == null) {
                        DocumentsDO docDO = BeanUtils.toBean(importDoc, DocumentsDO.class);
                        docDO.setInstitutionId(institutionId);

                        processFile(docDO, importDoc); // 下载并上传文件到Minio
                        supplementEntityAttributes(docDO, importDoc);
                        documentsMapper.insert(docDO);

                        // 添加记录
                        respVO.getCreateRecords().add(title);
                        Long docId = docDO.getId();
                        batchInsertList.add(docDO);
                        // 处理作者信息
                        processAuthors(importDoc.getAuthors(), institutionId, docId, batchAuthorList);


                    } else {
                        // 补充属性并判断是否需要更新
                        if (existDoc != null && existDoc.getPublishYear() == null
                                || existDoc.getPublishMonth() == null) {
                            supplementEntityAttributes(existDoc, importDoc);
                            documentsMapper.updateById(existDoc);
                            respVO.getUpdateRecords().add(title);
                        } else {
                            respVO.getIgnoreRecords().add("【已存在无需更新】：" + title);
                        }
                    }
                } catch (Exception e) {
                    respVO.getFailureRecords().put(title, "文献处理失败: " + e.getMessage());
                    e.printStackTrace();
                }
            }, executorService)
            .exceptionally(ex -> {
                respVO.getFailureRecords().put(title, "文献处理失败: " + ex.getMessage());
                return null;
            })
//            .orTimeout(600, TimeUnit.SECONDS) // 设置超时时间
            .exceptionally(ex -> {
                respVO.getFailureRecords().put(title, "文献处理超时");
                return null;
            });

            futures.add(future);
        }

        // 等待所有任务完成（最长耗时等于最长的单个任务）
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            log.error("文献导入任务异常", e);
        }

        // 批量提交作者关联关系
        if (!batchAuthorList.isEmpty()) {
            documentExpertsService.batchCreate(batchAuthorList);
        }

        // 添加到同步ES队列
        try {
            findUnSyncTask.manualSync();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return respVO;
    }

    private void processAuthors(String authorsStr, Long institutionId, Long docId, List<DocumentExpertsDO> batchAuthorList) {
        Map<String, Long> authorCache = new HashMap<>();
        if (StringUtils.isEmpty(authorsStr)) {
            return;
        }
        Arrays.stream(authorsStr.split(","))
                .filter(StringUtils::isNotBlank)
                .forEach(author -> {
                    Long expertId = authorCache.computeIfAbsent(author,
                            name -> expertsService.getOrCreateExpert(institutionId, name).getId()
                    );
                    batchAuthorList.add(new DocumentExpertsDO(docId, expertId));
                });
    }

    private boolean needsUpdate(DocumentsDO existDoc, DocumentImportExcelVO importDoc) {
        return Objects.equals(existDoc.getPublishYear(), importDoc.getPublishYear())
                ? (Objects.equals(existDoc.getPublishMonth(), importDoc.getPublishMonth()))
                : true;
    }
//    public DocumentImportRespVO importDocumentExcel(List<DocumentImportExcelVO> documentList) {
//        DocumentImportRespVO respVO = DocumentImportRespVO.builder()
//                .createRecords(new ArrayList<>())
//                .updateRecords(new ArrayList<>())
//                .failureRecords(new HashMap<>()).build();
//        // 筛选文档地址不为空的记录
//        List<DocumentImportExcelVO> importList =
//                documentList.stream().filter(document -> StringUtils.isNotBlank(document.getOriginalUrl()))
//                        .collect(Collectors.toList());
//
//        importList.forEach(importDocument -> {
//            Long institutionId;
//            try {
//                // 2.1.1 校验字段是否符合要求
//                institutionId = validateDocumentPublisher(importDocument.getPublisher());
//                ValidationUtils.validate(BeanUtils.toBean(importDocument, DocumentsSaveReqVO.class).setInstitutionId(institutionId));
//            } catch (Exception ex){
//                respVO.getFailureRecords().put(importDocument.getForeignTitle(), ex.getMessage());
//                return;
//            }
//
////            DocumentsDO existDocument = documentsMapper.selectByTitle(importDocument.getForeignTitle(), institutionId);
//            DocumentsDO existDocument = documentsMapper.selectBySource(importDocument.getDataSource());
//            // 保存新数据
//            if (existDocument == null) {
//                try {
//                    DocumentsDO documentDO = BeanUtils.toBean(importDocument, DocumentsDO.class).setInstitutionId(institutionId);
//                    // 根据文件地址下载文件并上传至Minio存储
//                    processFile(documentDO, importDocument);
//                    // 补充实体属性
//                    supplementEntityAttributes(documentDO, importDocument);
//                    // 保存文档信息
//                    documentsMapper.insert(documentDO);
//                    Long docId = documentDO.getId();
//
//                    // 处理作者信息
//                    if (StringUtils.isNotBlank(importDocument.getAuthors())) {
//                        CollectionUtil.toList(importDocument.getAuthors().split(",")).forEach(author -> {
//                            if (StringUtils.isNotBlank(author)) {
//                                // 根据作者名称查询作者信息，如果不存在则创建作者信息
//                                ExpertsDO expert = expertsService.getOrCreateExpert(institutionId, author);
//                                if (expert != null) {
//                                    // 添加作者与文档的关系
//                                    try {
//                                        DocumentExpertsSaveReqVO reqVO = new DocumentExpertsSaveReqVO();
//                                        reqVO.setDocumentId(docId);
//                                        reqVO.setExpertId(expert.getId());
//                                        documentExpertsService.createDocumentExperts(reqVO);
//                                    } catch (Exception e) {
////                                    e.printStackTrace();
//                                        System.out.println("文献作者已存在");
//                                    }
//                                }
//                            }
//                        });
//                    }
//                    respVO.getCreateRecords().add(importDocument.getForeignTitle());
//                    return;
//                } catch (IOException e) {
//                    respVO.getFailureRecords().put(importDocument.getForeignTitle(), "文献下载失败");
//                }
//            }
//            // 修改
////            DocumentsDO updateDO = BeanUtils.toBean(importDocument, DocumentsDO.class).setId(existDocument.getId());
//            // 补充实体属性
//            try {
//                if (existDocument != null && existDocument.getPublishYear() == null || existDocument.getPublishMonth() == null) {
//                    supplementEntityAttributes(existDocument, importDocument);
//                    documentsMapper.updateById(existDocument);
//                    respVO.getUpdateRecords().add(importDocument.getForeignTitle());
//                }
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
//        });
//        return respVO;
//    }

    /**
     * 补充实体属性
     * @param documentDO
     * @param importVO
     */
    private void supplementEntityAttributes(DocumentsDO documentDO, DocumentImportExcelVO importVO) throws IOException {
        String publishDate = importVO.getPublishDate();
        // 处理发布日期
        if (StringUtils.isNotBlank(publishDate)) {
            try {
                String[] dates = convertDate(publishDate).split("-");
                // 确保至少有年份
                if (dates.length >= 1) {
                    documentDO.setPublishYear(Integer.parseInt(dates[0]));
                }
                // 如果有月份，则设置月份
                if (dates.length >= 2) {
                    documentDO.setPublishMonth(Integer.parseInt(dates[1]));
                }
                // 如果有日期，则设置日期
                if (dates.length >= 3) {
                    documentDO.setPublishDay(Integer.parseInt(dates[2]));
                }
            } catch (IllegalArgumentException e) {
                e.printStackTrace();
            }

        }
        // 处理标签
        if (StringUtils.isNotBlank(importVO.getTags())) {
            documentDO.setTags(importVO.getTags().replaceAll("；", ",").replaceAll(";", ",").replaceAll("、", ",").replaceAll("，", ",").split(","));
        }

        // 调用大模型获取分类信息
        if (null == documentDO.getCategoryId()) {
            documentDO.setCategoryId(processClassification(importVO));
        }

        // 创建抽取器
        DocumentInfoExtractor extractor = new DocumentInfoExtractor();
        // 翻译标题和摘要
        if (StringUtils.isNotBlank(importVO.getForeignTitle())
                && StringUtils.isBlank(documentDO.getChineseTitle())) {
            documentDO.setChineseTitle(extractor.translateText(importVO.getForeignTitle()));
        }
        if (StringUtils.isNotBlank(importVO.getForeignAbstract())
                && StringUtils.isBlank(documentDO.getChineseAbstract())) {
            documentDO.setChineseAbstract(extractor.translateText(importVO.getForeignAbstract()));
        }
    }

    /**
     * 处理文件信息
     * @param documentDO 文档对象
     * @param importVO 导入数据对象
     */
    private void processFile(DocumentsDO documentDO, DocumentImportExcelVO importVO) throws IOException {
        String originalUrl = importVO.getOriginalUrl();
        File file = null;
        String fileName = null;
        String suffix = "pdf";

        // 处理有原始URL的情况
        if (StringUtils.isNotBlank(originalUrl)) {
            // 下载文件并上传至Minio存储
            file = FileDownloader.downloadFileWithTimeout(originalUrl,
                    600,
                    TimeUnit.SECONDS);

            // 文件地址包含多个地址，取第一个
            if (originalUrl.contains(",")) {
                originalUrl = originalUrl.split(",")[0];
            }
            fileName = originalUrl.substring(originalUrl.lastIndexOf("/") + 1);
            int queryIndex = fileName.indexOf('?');
            if (queryIndex != -1) {
                fileName = fileName.substring(0, queryIndex);
            }
            suffix = FileUtil.getSuffix(fileName);
        }
        // 处理没有原始URL但有数据源URL的情况
        else if (StringUtils.isNotBlank(importVO.getDataSource())) {
            try {
                // 将网页转换为PDF
                log.info("没有PDF文件地址，尝试从数据源URL转换: {}", importVO.getDataSource());
                file = convertWebPageToPdf(importVO.getDataSource());

                // 生成文件名
                String dataSourceUrl = importVO.getDataSource();
                if (dataSourceUrl.contains(",")) {
                    dataSourceUrl = dataSourceUrl.split(",")[0];
                }

                // 从URL中提取文件名，如果无法提取则使用标题
                if (dataSourceUrl.contains("/")) {
                    fileName = dataSourceUrl.substring(dataSourceUrl.lastIndexOf("/") + 1);
                    int queryIndex = fileName.indexOf('?');
                    if (queryIndex != -1) {
                        fileName = fileName.substring(0, queryIndex);
                    }
                    // 如果文件名为空或太短，使用标题
                    if (fileName.length() < 5) {
                        fileName = importVO.getForeignTitle();
                    }
                } else {
                    fileName = importVO.getForeignTitle();
                }

                // 确保文件名有.pdf后缀
                if (!fileName.toLowerCase().endsWith(".pdf")) {
                    fileName = fileName + ".pdf";
                }

                suffix = "pdf";
            } catch (Exception e) {
                log.error("从数据源URL转换PDF失败: {}", e.getMessage(), e);
                throw new IOException("从数据源URL转换PDF失败: " + e.getMessage(), e);
            }
        } else {
            // 既没有原始URL也没有数据源URL，无法处理
            log.warn("文档既没有原始URL也没有数据源URL，无法处理文件: {}", importVO.getForeignTitle());
            return;
        }

        if (file != null) {
            String id = FileHashUtil.generateFileHash(file);
            String path = "document/" + id + "." + suffix;

            // 读取文件内容
            byte[] fileContent = IoUtil.readBytes(new FileInputStream(file));

            // 使用重试机制上传文件至Minio
            try {
                String fileUrl = uploadFileWithRetry(fileName, path, fileContent);
                documentDO.setOriginalUrl(fileUrl);

                if ("pdf".equalsIgnoreCase(suffix)) {
                    // 生成缩略图
                    DocumentInfo documentInfo = processPDF(new ByteArrayInputStream(fileContent), id);
                    if (null != documentInfo) {
                        documentDO.setPageCount(documentInfo.getPageCount());
                        documentDO.setWordCount(documentInfo.getWordCount());
                        documentDO.setThumbnailUrl(documentInfo.getSmallThumbnailPath());

                        if (ArrayUtils.isEmpty(documentDO.getTags()) && StringUtils.isNotBlank(documentInfo.getTags())) {
                            documentDO.setTags(documentInfo.getTags().replaceAll("；", ",").replaceAll(";", ",").replaceAll("、", ",").replaceAll("，", ",").split(","));
                        }
                    }
                }
            } catch (Exception e) {
                log.error("文件上传失败: {}", e.getMessage());
                throw new IOException("文件上传失败: " + e.getMessage(), e);
            } finally {
                // 清理临时文件
                file.deleteOnExit();
            }
        }
    }

    /**
     * 生成缩略图
     * @param file
     * @param id
     * @return
     */
//    private DocumentInfo generateThumbnail(File file, String id) throws FileNotFoundException {
//        DocumentInfo documentInfo = new DocumentInfo();
//        PDFNet.initialize("demo:1736921460852:7e8522c903000000001a52ff8186f4aa786f2eec6fc3ca92686daa842f");
//        StringBuilder fullTextBuilder = new StringBuilder();
//        Integer pageCount = 0;
//
//        // 定义缩略图保存目录
//        String thumbnailDir = "thumbnails";
//        File dir = new File(thumbnailDir);
//        if (!dir.exists()) {
//            dir.mkdirs(); // 创建目录
//        }
//        // 生成缩略图路径
//        String baseDir = System.getProperty("user.dir");
//        String largeThumbnailPath = Paths.get(baseDir, thumbnailDir, id + "_thumbnail_large.png").toString();
//        String smallThumbnailPath = Paths.get(baseDir, thumbnailDir, id + "_thumbnail_small.png").toString();
//
//        try (PDFDoc doc = new PDFDoc(new FileInputStream(file))) {
//            PDFDraw draw = new PDFDraw();
//            draw.setDPI(96); // 96 DPI 是常见的屏幕分辨率
//
//            pageCount = doc.getPageCount();
//
//            TextExtractor extractor = new TextExtractor();
//
//            for (int i = 1; i < pageCount + 1; i++) {
//
//                com.pdftron.pdf.Page page = doc.getPage(i);
//
//                if (i == 1) {
//                    // 生成大缩略图
//                    BufferedImage image = draw.getBitmap(page);
//                    File largeOutputFile = new File(largeThumbnailPath);
//                    ImageIO.write(image, "png", largeOutputFile);
//
//                    // 生成小缩略图
//                    draw.setImageSize(160, 210);
//                    BufferedImage smallImage = draw.getBitmap(page);
//                    File smallOutputFile = new File(smallThumbnailPath);
//                    ImageIO.write(smallImage, "png", smallOutputFile);
//                }
//
//                // 提取文本
//                extractor.begin(page);
//                String text = extractor.getAsText();
//                fullTextBuilder.append(text);
//            }
//        } catch (PDFNetException | IOException e) {
//            e.printStackTrace();
//        } finally {
//            PDFNet.terminate();
//        }
//
//        // 保存缩略图
//        File smallThumbnail = new File(smallThumbnailPath);
//        String smallPath = "document/" + smallThumbnail.getName();
//        String smallThumbnailUrl = fileApi.createFile(smallThumbnail.getName(), smallPath, IoUtil.readBytes(new FileInputStream(smallThumbnail)));
//        FileUtil.del(smallThumbnailPath);
//
//        File largeThumbnail = new File(largeThumbnailPath);
//        String largePath = "document/" + largeThumbnail.getName();
//        String largeThumbnailUrl = fileApi.createFile(smallThumbnail.getName(), largePath, IoUtil.readBytes(new FileInputStream(largeThumbnail)));
//        FileUtil.del(largeThumbnail);
//
//        // 处理提取的文本
//        String fullText = fullTextBuilder.toString();
//        int wordCount = fullText.split("\\s+").length; // 简单的字数统计
//        // 创建抽取器
//        DocumentInfoExtractor extractor = new DocumentInfoExtractor();
//        // 抽取信息
//        documentInfo = extractor.extractInfoFromText(fullText.substring(0, Math.min(fullText.length(), 7500)));
//
//
//        documentInfo.setWordCount(wordCount);
//        documentInfo.setPageCount(pageCount);
//        documentInfo.setSmallThumbnailPath(smallThumbnailUrl);
//        documentInfo.setLargeThumbnailPath(largeThumbnailUrl);
//        documentInfo.setFullText(fullText);
//
//        return documentInfo;
//    }

    private Long processClassification(DocumentImportExcelVO importVO) {
        // 创建抽取器
        DocumentInfoExtractor extractor = new DocumentInfoExtractor();

        List<CategoriesDO> leafCategories = categoriesService.getLeafCategories();
        List<String> classificationList = leafCategories.stream().map(CategoriesDO::getName).collect(Collectors.toList());

        // 处理分类，根据摘要/简介通过大模型计算分类
        String content = null;
        if (StringUtils.isNotBlank(importVO.getIntroduction())) {
            content = importVO.getIntroduction();
        } else if (StringUtils.isNotBlank(importVO.getForeignAbstract())) {
            content = importVO.getForeignAbstract();
        }
        if (StringUtils.isNotBlank(content)) {
            // 调用大模型获取分类
            String classification = extractor.classify(content, classificationList);
            if (StringUtils.isNotBlank(classification)) {
                Long categoryId = leafCategories.stream() // 将列表转换为Stream
                        .filter(category -> classification.equals(category.getName())) // 过滤出名称匹配的分类
                        .map(CategoriesDO::getId) // 提取ID
                        .findFirst() // 获取第一个匹配的结果
                        .orElse(null);// 如果没有找到匹配的结果，则返回null
                return categoryId;
//                if (categoryId != null) {
//                    documentDO.setCategoryId(categoryId);
//                }
            }
        }
        return null;
    }

    Map<String, Long> publisherMap = new HashMap<>();
    /**
     * 验证发布机构
     * @param publisher
     * @return
     */
    private Long validateDocumentPublisher(String publisher) {
        if (StrUtil.isBlank(publisher)) {
            throw new ServiceException(DOCUMENT_PUBLISHER_NOT_EXISTS);
        }
        if (publisherMap.containsKey(publisher)) {
            return publisherMap.get(publisher);
        }
        InstitutionsDO institution = institutionsService.getInstitutionByName(publisher);
        if (institution == null) {
            throw new ServiceException(DOCUMENT_PUBLISHER_NOT_EXISTS);
        } else {
            publisherMap.put(publisher, institution.getId());
            return institution.getId();
        }
    }

    public void updateSyncStatus(Long id) {
        documentsMapper.updateSyncStatus(id);
    }

    @Override
    public boolean updateSyncStatusWithValue(Long id, Short status) {
        if (id == null) {
            log.error("更新文档同步状态失败，ID为空");
            return false;
        }

        try {
            // 校验文档是否存在
            DocumentsDO document = documentsMapper.selectById(id);
            if (document == null) {
                log.error("更新文档同步状态失败，文档不存在，ID={}", id);
                return false;
            }

            // 更新同步状态
            int rows = documentsMapper.updateSyncStatusWithValue(id, status);

            if (rows > 0) {
                log.info("成功更新文档同步状态，ID={}, 新状态={}", id, status);
                return true;
            } else {
                log.warn("更新文档同步状态失败，数据库未受影响，ID={}", id);
                return false;
            }
        } catch (Exception e) {
            log.error("更新文档同步状态时发生异常，ID={}", id, e);
            return false;
        }
    }

    public List<DocumentsDO> getUnSyncRecords() {
        return documentsMapper.selectList(new QueryWrapper<DocumentsDO>().eq("sync_status", 0).eq("deleted", 0));
    }

    /**
     * 将网页转换为PDF文件
     *
     * @param url 网页URL
     * @return 临时PDF文件
     * @throws IOException 如果转换过程中发生错误
     */
    private File convertWebPageToPdf(String url) throws IOException {
        log.info("开始将网页转换为PDF: {}", url);

        try {
            // 使用ProxyController获取网页内容
            String htmlContent = proxyController.proxy(url);
            if (htmlContent == null || htmlContent.isEmpty() || htmlContent.startsWith("请求失败")) {
                log.error("获取网页内容失败: {}", htmlContent);
                throw new IOException("获取网页内容失败: " + htmlContent);
            }

            // 创建临时HTML文件
            File tempHtmlFile = File.createTempFile("webpage_", ".html");
            FileUtil.writeString(htmlContent, tempHtmlFile, "UTF-8");

            // 创建临时PDF文件
            File tempPdfFile = File.createTempFile("webpage_", ".pdf");

            // 使用Aspose.PDF转换HTML到PDF
            // 设置基础URL，用于解析相对路径
            String baseUrl = url.substring(0, url.lastIndexOf('/') + 1);
            HtmlLoadOptions options = new HtmlLoadOptions(baseUrl);

            // 设置页面信息
            PageInfo pageInfo = new PageInfo();
            pageInfo.setWidth(PageSize.A4.getWidth());
            pageInfo.setHeight(PageSize.A4.getHeight());
            options.setPageInfo(pageInfo);

            // 设置其他选项
            options.setEmbedFonts(true);
            options.setRenderToSinglePage(false);

            // 创建PDF文档
            Document pdfDocument = new Document(tempHtmlFile.getAbsolutePath(), options);

            // 保存为PDF
            pdfDocument.save(tempPdfFile.getAbsolutePath(), SaveFormat.Pdf);

            // 清理临时HTML文件
            tempHtmlFile.delete();

            log.info("网页转换为PDF完成: {}", tempPdfFile.getAbsolutePath());
            return tempPdfFile;
        } catch (Exception e) {
            log.error("网页转换为PDF失败: {}", e.getMessage(), e);
            throw new IOException("网页转换为PDF失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用重试机制上传文件
     *
     * @param fileName 文件名
     * @param path 文件路径
     * @param content 文件内容
     * @return 文件URL
     * @throws Exception 如果上传失败
     */
    private String uploadFileWithRetry(String fileName, String path, byte[] content) throws Exception {
        // 定义重试参数
        final int maxRetries = 5; // 最大重试次数（增加到5次）
        final long initialBackoffMillis = 2000; // 初始退避时间（2秒）
        final long maxBackoffMillis = 30000; // 最大退避时间（30秒）

        log.info("开始上传文件: {}, 路径: {}, 大小: {} 字节", fileName, path, content.length);

        // 定义重试条件：只有当异常是MinIO的资源不可写错误时才重试
        try {
            return RetryUtil.retryWithExponentialBackoff(
                    // 要执行的操作
                    (Callable<String>) () -> {
                        try {
                            log.debug("尝试上传文件: {}", path);
                            return fileApi.createFile(fileName, path, content);
                        } catch (Exception e) {
                            log.warn("文件上传失败: {}, 错误: {}", path, e.getMessage());
                            throw e;
                        }
                    },
                    // 判断是否需要重试的条件
                    (Exception e) -> {
                        // 检查是否是MinIO的资源不可写错误
                        Throwable cause = e;
                        while (cause != null) {
                            if (cause instanceof ErrorResponseException) {
                                String message = cause.getMessage();
                                if (message != null && message.contains("Resource requested is unwritable")) {
                                    log.warn("MinIO资源不可写，将进行重试: {}", message);
                                    return true; // 需要重试
                                }
                            }
                            cause = cause.getCause();
                        }
                        log.error("文件上传失败，不符合重试条件: {}", e.getMessage());
                        return false; // 不需要重试
                    },
                    maxRetries,
                    initialBackoffMillis,
                    maxBackoffMillis
            );
        } catch (Exception e) {
            log.error("文件上传失败，已达到最大重试次数: {}, 错误: {}", maxRetries, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public boolean updateDocumentUrl(Long id, String newUrl) {
        if (id == null || StringUtils.isEmpty(newUrl)) {
            log.error("更新文档URL失败，参数无效，ID={}, URL={}", id, newUrl);
            return false;
        }

        try {
            // 校验文档是否存在
            DocumentsDO document = documentsMapper.selectById(id);
            if (document == null) {
                log.error("更新文档URL失败，文档不存在，ID={}", id);
                return false;
            }

            // 更新URL
            DocumentsDO updateObj = new DocumentsDO();
            updateObj.setId(id);
            updateObj.setOriginalUrl(newUrl);
            int rows = documentsMapper.updateById(updateObj);

            if (rows > 0) {
                log.info("成功更新文档URL，ID={}, 新URL={}", id, newUrl);
                return true;
            } else {
                log.warn("更新文档URL失败，数据库未受影响，ID={}", id);
                return false;
            }
        } catch (Exception e) {
            log.error("更新文档URL时发生异常，ID={}", id, e);
            return false;
        }
    }

}